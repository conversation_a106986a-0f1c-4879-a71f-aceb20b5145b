<template>
  <div class="unfinished">
    <div class="unfinished-bg common-bg text-left">
      <div class="unfinished-title common-title padding-left-xll">久拖不决</div>
    </div>
    <div class="unfinished-content">
      <div class="unfinished-left">
        <div class="unfinished-left-value">{{ totality }}</div>
      </div>
      <div class="unfinished-right">
        <div class="unfinished-right-title">
          <div class="unfinished-right-title-text">超</div>
          <div class="unfinished-right-title-actions">
            <div class="unfinished-right-title-action" :class="{ active: selectedDays === 15 }"
              @click="handleClick(15)">15</div>
            <div class="unfinished-right-title-action" :class="{ active: selectedDays === 30 }"
              @click="handleClick(30)">30</div>
            <div class="unfinished-right-title-action" :class="{ active: selectedDays === 60 }"
              @click="handleClick(60)">60</div>
          </div>
          <div class="unfinished-right-title-text">天</div>
        </div>
        <!-- 滚动列表容器 -->
        <div class="unfinished-list overflow-hidden" ref="scrollContainer">
          <div v-if="displayData.length" class="scroll-wrapper" ref="scrollWrapper">
            <div class="unfinished-list-item" v-for="(item, index) in displayData" :key="item.id || index"
              @mouseenter="handleHover(index)" @mouseleave="handleMouseLeave(index)">
              <div class="unfinished-list-item-title">{{ item.name }}</div>
              <div class="unfinished-list-item-content">{{ item.value }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue';

// 响应式变量
const selectedDays = ref(15);
const totality = ref(4293);
const scrollContainer = ref(null); // 滚动容器DOM引用
const scrollWrapper = ref(null); // 滚动内容DOM引用
const originalData = ref([]); // 存储原始数据
const displayData = ref([]);  // 用于显示的数据（原始数据 + 克隆项，实现无缝滚动）
const currentIndex = ref(0); // 当前滚动到的索引
const timer = ref(null); // 滚动计时器ID
const hoverState = reactive([]); // 记录列表项的hover状态
const isScrolling = ref(true); // 是否正在自动滚动

const itemHeight = ref(0); // 单个列表项的高度
const containerHeight = ref(0); // 滚动容器的高度
const visibleItemCount = 3; // 同时显示的列表项数量
const isTransitioning = ref(false); // 是否正在滚动过渡动画中

// 初始化状态数组
const initStates = () => {
  hoverState.length = 0;
  displayData.value.forEach((_, index) => {
    hoverState[index] = false;
  });
};

// 事件处理
const handleClick = (days) => {
  console.log(days);
  selectedDays.value = days;
};

const handleHover = (index) => {
  hoverState[index] = true;
  stopScrolling(); // 悬停时停止滚动
};

const handleMouseLeave = (index) => {
  hoverState[index] = false;
  resumeScrolling(); // 离开时恢复滚动
};

// 滚动逻辑
const startScrolling = () => {
  if (timer.value) clearInterval(timer.value);

  const originalLength = originalData.value.length;

  if (originalLength > visibleItemCount) {
    isScrolling.value = true;
    timer.value = setInterval(scrollToNextItem, 3000); // 每3秒滚动一次
  } else {
    isScrolling.value = false;
  }
};

const stopScrolling = () => {
  isScrolling.value = false;
  clearInterval(timer.value);
};

const resumeScrolling = () => {
  isScrolling.value = true;
  startScrolling();
};

const scrollToNextItem = () => {
  if (isTransitioning.value || !isScrolling.value) return;

  if (!scrollWrapper.value || itemHeight.value === 0) return;

  isTransitioning.value = true;
  currentIndex.value++;

  const translateY = -currentIndex.value * itemHeight.value;

  scrollWrapper.value.style.transition = 'transform 0.5s ease-in-out';
  scrollWrapper.value.style.transform = `translateY(${translateY}px)`;

  const originalLength = originalData.value.length;
  if (currentIndex.value >= originalLength) {
    setTimeout(() => {
      scrollWrapper.value.style.transition = 'none';
      scrollWrapper.value.style.transform = 'translateY(0)';
      currentIndex.value = 0;
      isTransitioning.value = false;
    }, 500);
  } else {
    setTimeout(() => {
      isTransitioning.value = false;
    }, 500);
  }
};

const calculateDimensions = () => {
  if (scrollWrapper.value && scrollWrapper.value.children.length > 0) {
    const firstChild = scrollWrapper.value.children[0];
    const computedStyle = window.getComputedStyle(firstChild);
    const marginBottom = parseInt(computedStyle.marginBottom) || 23;
    itemHeight.value = firstChild.offsetHeight + marginBottom;

    // 不需要动态设置容器高度，已在CSS中固定设置
    containerHeight.value = itemHeight.value * visibleItemCount;
  }
};

// 准备用于无缝滚动的数据
const prepareSeamlessData = () => {
  // 模拟数据 - 更多数据用于测试滚动效果
  const mockData = [
    { id: 1, name: '浑南区', value: 85 },
    { id: 1, name: '浑南区', value: 85 },
    { id: 1, name: '浑南区', value: 85 },
    { id: 1, name: '浑南区', value: 85 },
    { id: 1, name: '浑南区', value: 85 },
  ];

  originalData.value = [...mockData];
  displayData.value = [...mockData];

  // 只有当数据量大于可见项数量时才添加克隆项（用于无缝滚动）
  if (mockData.length > visibleItemCount) {
    const cloneCount = visibleItemCount;
    for (let i = 0; i < cloneCount; i++) {
      displayData.value.push(mockData[i % mockData.length]);
    }
  }

  initStates();
};

// 监听显示数据变化
watch(() => displayData.value, () => {
  initStates();
  nextTick(calculateDimensions);
}, { deep: true });

// 组件挂载时初始化
onMounted(() => {
  prepareSeamlessData();

  nextTick(() => {
    calculateDimensions();
    startScrolling();
  });

  window.addEventListener('resize', calculateDimensions);
});

// 组件卸载时清理
onUnmounted(() => {
  stopScrolling();
  window.removeEventListener('resize', calculateDimensions);
});

</script>
<style scoped>
.unfinished {
  width: 738px;
}

.unfinished-bg {
  background-image: url('../../src/assets/img/huawu.png');
  background-size: 100% 41px;
  height: 41px;
  margin-bottom: 11px;
}

.unfinished-title {
  height: 21px;
  padding-left: 97px;
  padding-bottom: 10px;
}

.unfinished-content {
  display: flex;
}

.unfinished-left {
  width: 254px;
  height: 301px;
  margin-left: 31px;
  background-image: url('../../src/assets/img/unfinished.png');
}

.unfinished-left-value {
  padding-top: calc(199px - (36px - 26px));
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 36px;
  line-height: 36px;
  color: #FFFFFF;
  text-shadow: 0px 4px 5px rgba(13, 4, 8, 0.2);
  background: linear-gradient(180deg, #8ED2FF 0%, #FFFFFF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unfinished-right {
  margin-left: 9px;
}

.unfinished-right-title {
  height: 36px;
  width: 435px;
  background-image: url('../../src/assets/img/footer-title.png');
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding-bottom: 10px;
}

.unfinished-right-title-actions {
  display: flex;
  gap: 8px;
}

.unfinished-right-title-action {
  width: 32px;
  height: 24px;
  background-image: url('../../src/assets/img/switch.png');

  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #6C9CE3;
  line-height: 24px;
  cursor: pointer;
}

.unfinished-right-title-action.active {
  color: #E3AE3B;
  background-image: url('../../src/assets/img/switch-select.png');
}

.unfinished-right-title-text {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #6C9CE3;
  line-height: 11px;
}

/* 滚动区域样式 */
.unfinished-list {
  overflow: hidden;
  /* 隐藏超出容器的内容，实现滚动效果 */
  height: 270px;
}

.scroll-wrapper {
  transition: transform 0.5s ease-in-out;
}

.unfinished-list-item {
  width: 434px;
  height: 68px;
  background-image: url('../../src/assets/img/unfinished-list-bg.png');
  display: flex;
  line-height: 68px;
  position: relative;
  margin-bottom: 23px;
}

.unfinished-list-item:last-child {
  margin-bottom: 0;
}

.unfinished-list-item-title {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 15px;
  color: #FEFEFE;
  position: absolute;
  left: 142px;
}

.unfinished-list-item-content {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 24px;
  color: #C5983A;
  text-shadow: 0px 4px 5px rgba(13, 4, 8, 0.2);
  position: absolute;
  left: 238px;
  color: #00FECB;
  text-shadow: 0px 4px 5px rgba(13, 4, 8, 0.2);
  background: linear-gradient(180deg, #C5983A 0%, #FFFFFF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
